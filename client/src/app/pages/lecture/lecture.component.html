<div class="middle_stripe">
  <breadcrumb></breadcrumb>
  <div class="tab-container relative" *ngIf="track()">
    <div class="wrapper_line custom_">
      <div class="dec_head _background">
        <img class="carn_b_" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
        <h1 class="dec_head-title_">{{track().title}}</h1>
        <img class="carn_b_ rotate" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
      </div>
    </div>
    <div class="flex relative justify-center tabs_w">
      <div class="lecture-tabs">
        <ng-container *ngFor="let tab of lectureTabs">
          <ng-container *ngIf="tab !== LectureTab.VIDEO || (tab === LectureTab.VIDEO && sanitizedVideoUrl)">
            <div class="lecture-tab" [ngClass]="{'is-active': activeTab === tab }" (click)="selectTab(tab)">
              {{tab}}
            </div>
          </ng-container>
        </ng-container>
      </div>
    </div>

    <div class="tab-content" [ngSwitch]="activeTab" [ngClass]="{'video-tab-content': activeTab === LectureTab.VIDEO}">
      <div class="flex flex-col items-center w-full" *ngSwitchCase="LectureTab.AUDIO">
        <div class="flex items-center justify-between w-full pb-3 audio-head">
          <div class="flex info-head">
            <div class="flex gap-[6px] items-center">
              <span class="desktop-duration">Длительность:</span>
              <span class="mobile-duration">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M7.5 0C7.83333 0 8.16667 0 8.5 0C8.54167 0.00835073 8.57917 0.0208768 8.62083 0.0250522C9.12083 0.112735 9.64167 0.141962 10.1208 0.292276C13.2792 1.26514 15.2042 3.37787 15.875 6.62213C15.9333 6.91023 15.9583 7.20668 16 7.49896C16 7.83298 16 8.16701 16 8.50104C15.9917 8.5428 15.9792 8.58037 15.975 8.62213C15.8917 9.72025 15.6125 10.7683 15.075 11.7328C13.5625 14.4301 11.2583 15.8747 8.16667 15.9958C6.50417 16.0626 4.9625 15.5992 3.57917 14.6681C1.69583 13.3904 0.541667 11.6326 0.125 9.39457C0.0708333 9.09812 0.0416667 8.79749 0 8.50104C0 8.16701 0 7.83298 0 7.49896C0.00833333 7.45303 0.0208333 7.4071 0.025 7.36117C0.0791667 6.99791 0.1125 6.6263 0.1875 6.26722C0.833333 3.16493 3.47917 0.634655 6.60417 0.125261C6.90417 0.0751566 7.2 0.0417536 7.5 0ZM1.28333 8.6263C1.58333 12.0084 4.36667 14.4885 7.375 14.7098C7.375 14.5344 7.375 14.3591 7.375 14.1795C7.37917 13.7954 7.6375 13.524 8 13.524C8.3625 13.524 8.62083 13.7954 8.625 14.1795C8.625 14.3549 8.625 14.5303 8.625 14.7098C11.8583 14.476 14.5 11.7161 14.7042 8.6263C14.625 8.6263 14.55 8.6263 14.4708 8.6263C13.8083 8.6263 13.5208 8.43424 13.5208 7.99582C13.525 7.56576 13.8083 7.37369 14.4625 7.37369C14.5458 7.37369 14.625 7.37369 14.7083 7.37369C14.4333 3.9499 11.525 1.46138 8.625 1.29854C8.625 1.46973 8.625 1.64092 8.625 1.81211C8.62083 2.20459 8.3625 2.48017 7.99167 2.47599C7.63333 2.47182 7.375 2.20042 7.375 1.81211C7.375 1.63674 7.375 1.46138 7.375 1.28601C3.83333 1.59499 1.4375 4.60125 1.3 7.37787C1.4625 7.37787 1.62083 7.37787 1.78333 7.37787C2.19583 7.37787 2.47083 7.62839 2.475 8C2.475 8.37578 2.2 8.6263 1.775 8.63048C1.61667 8.6263 1.45417 8.6263 1.28333 8.6263Z"
                    fill="var(--font-color1)" />
                  <path
                    d="M7.37515 6.15856C7.37515 5.56148 7.37515 4.96023 7.37515 4.36315C7.37515 3.97067 7.63765 3.69092 8.00015 3.69092C8.36265 3.69092 8.62515 3.97067 8.62515 4.36733C8.62515 5.43622 8.62932 6.50094 8.62098 7.56983C8.62098 7.69509 8.65432 7.78278 8.74182 7.87046C9.45015 8.57192 10.1501 9.27756 10.8543 9.97902C11.0043 10.1293 11.1001 10.3047 11.0793 10.5218C11.0543 10.7765 10.921 10.9602 10.6793 11.0521C10.4251 11.1481 10.196 11.0897 10.0043 10.8976C9.55848 10.4592 9.12098 10.0166 8.67932 9.57401C8.32932 9.22328 7.98348 8.87255 7.62932 8.52599C7.45432 8.3548 7.37098 8.16273 7.37515 7.91639C7.37932 7.32349 7.37515 6.73893 7.37515 6.15856Z"
                    fill="var(--font-color1)" />
                </svg>
              </span>
              <span>
                {{Math.ceil(track().duration / 60)}} мин
              </span>
            </div>
            <div class="flex gap-[6px] items-center">
              <span class="auditions-desktop">Кол-во прослушиваний:</span>
              <span class="auditions-mobile">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="16" viewBox="0 0 20 16" fill="none">
                  <path
                    d="M18.1442 14.646C18.0874 14.8073 18.0448 14.9733 17.9644 15.1251C17.6661 15.6801 17.1974 15.9837 16.5678 15.9932C15.8672 16.0026 15.1618 15.9979 14.4612 15.9932C14.1061 15.9932 13.8931 15.775 13.8931 15.4192C13.8931 13.3179 13.8931 11.2166 13.8931 9.12009C13.8931 8.75011 14.1061 8.5414 14.4754 8.5414C14.9819 8.5414 15.4885 8.5414 16.0139 8.5414C16.0897 6.03219 15.2281 3.94513 13.1972 2.46996C10.8634 0.776599 8.32119 0.582123 5.78377 1.93871C3.20847 3.31902 2.07704 5.61004 2.11965 8.5414C2.19539 8.5414 2.25694 8.5414 2.31848 8.5414C2.77768 8.5414 3.24161 8.5414 3.70081 8.5414C4.03692 8.5414 4.25468 8.7596 4.25468 9.09637C4.25468 11.2071 4.25468 13.3227 4.25468 15.4334C4.25468 15.7797 4.03692 15.9979 3.69134 15.9979C2.99071 15.9979 2.28534 16.0026 1.58471 15.9979C0.708917 15.9931 0.0035505 15.2864 0.0035505 14.4184C-0.0011835 12.9906 -0.0011835 11.5676 0.0035505 10.1399C0.0082845 9.47109 0.330197 8.99202 0.917213 8.69793C1.0403 8.63627 1.07344 8.57461 1.0687 8.44654C1.03083 7.39827 1.14445 6.36897 1.5137 5.38236C2.47943 2.79251 4.28309 1.06594 6.93886 0.321241C9.49523 -0.394999 11.8812 0.0935619 13.9973 1.71578C15.55 2.90635 16.511 4.49061 16.9276 6.40691C17.0744 7.08046 17.1028 7.7635 17.0838 8.45128C17.0791 8.56986 17.1075 8.63153 17.2306 8.68845C17.6898 8.9019 17.9786 9.26713 18.1111 9.76044C18.1206 9.79364 18.1348 9.8221 18.149 9.85056C18.1442 11.4538 18.1442 13.0476 18.1442 14.646ZM3.1848 9.60391C2.66879 9.60391 2.17172 9.60391 1.66992 9.60391C1.28647 9.60391 1.06397 9.8221 1.06397 10.2063C1.06397 11.5771 1.06397 12.9527 1.06397 14.3235C1.06397 14.7077 1.28173 14.9259 1.66992 14.9306C2.12912 14.9306 2.59305 14.9306 3.05225 14.9306C3.09485 14.9306 3.14219 14.9212 3.18953 14.9164C3.1848 13.1472 3.1848 11.3921 3.1848 9.60391ZM14.9677 9.60391C14.9677 11.3921 14.9677 13.1566 14.9677 14.9306C15.034 14.9306 15.0861 14.9306 15.1382 14.9306C15.6116 14.9306 16.085 14.9354 16.5536 14.9259C16.8566 14.9212 17.0744 14.6935 17.0791 14.3852C17.0838 12.9717 17.0838 11.5582 17.0791 10.1446C17.0791 9.84107 16.8566 9.61339 16.5536 9.60391C16.0281 9.59916 15.5027 9.60391 14.9677 9.60391Z"
                    fill="var(--font-color1)" />
                  <path
                    d="M9.60568 9.29554C9.60568 9.40938 9.60568 9.47105 9.60568 9.53745C9.60568 10.7897 9.60568 12.0467 9.60568 13.2989C9.60568 14.148 8.99499 14.85 8.19021 14.9306C7.33336 15.016 6.57118 14.4468 6.43863 13.5788C6.25874 12.4498 7.28128 11.5012 8.42218 11.7858C8.45532 11.7953 8.48845 11.8 8.54526 11.8142C8.54526 11.7384 8.54526 11.6767 8.54526 11.615C8.54526 10.4339 8.55 9.24811 8.54526 8.06703C8.54526 7.80614 8.63994 7.61641 8.87664 7.5168C9.10861 7.42194 9.31217 7.4836 9.48733 7.6591C10.1927 8.36585 10.9028 9.06787 11.6034 9.78411C12.3703 10.5715 12.3656 11.8569 11.6082 12.6206C11.3005 12.9289 10.8271 12.8341 10.6945 12.4309C10.6282 12.2364 10.6803 12.0609 10.8081 11.9044C11.2294 11.4063 11.2058 10.9083 10.7418 10.4434C10.3773 10.0734 10.0128 9.70347 9.60568 9.29554ZM8.54053 13.3274C8.53579 13.0428 8.28016 12.7961 8.00085 12.8009C7.72628 12.8103 7.48485 13.0522 7.48011 13.3274C7.47538 13.6072 7.73101 13.8634 8.01032 13.8634C8.29436 13.8634 8.54526 13.6072 8.54053 13.3274Z"
                    fill="var(--font-color1)" />
                </svg>
              </span>
              <span>
                {{track().listened}}
              </span>
            </div>
          </div>
          <div class="flex items-center head_action_panel">

            @if(audioService.hasAccess(track())) {
              <a [href]="track().link" download>Скачать</a>
            }

            <button class="icons_w lik_hov" (click)="share()">
              <div class="flex items-center icon-wrap star_w">
                <svg class="emty_f" xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 18 18"
                  fill="none">
                  <path
                    d="M11.9427 5.25594C10.1298 6.16264 8.32232 7.06399 6.49872 7.97606C6.70254 8.6467 6.7079 9.31734 6.49872 9.99871C8.31159 10.9054 10.1191 11.8121 11.9052 12.7027C12.1841 12.4452 12.4308 12.1662 12.7204 11.957C14.7264 10.4923 17.5905 11.7048 17.9499 14.1674C18.2609 16.3081 16.5124 18.1644 14.3509 17.982C12.817 17.8533 11.5297 16.6085 11.3634 15.0741C11.3313 14.7576 11.3366 14.4303 11.3795 14.1191C11.4063 13.9367 11.3581 13.8777 11.2133 13.808C9.65784 13.0354 8.10778 12.2574 6.55236 11.4795C6.37536 11.3883 6.193 11.3078 6.016 11.2059C5.90873 11.1468 5.84973 11.1629 5.76392 11.2542C5.07202 11.9838 4.22459 12.3379 3.21624 12.3272C1.66082 12.3057 0.244848 11.0556 0.0356701 9.51048C-0.216416 7.69171 0.888471 6.0929 2.69062 5.72807C3.87059 5.492 4.89503 5.8461 5.74246 6.70452C5.8551 6.81719 5.92482 6.82792 6.05891 6.75817C7.7806 5.88902 9.50766 5.0306 11.2347 4.17218C11.3688 4.1078 11.4117 4.04879 11.3795 3.88783C11.047 2.06905 12.3342 0.293196 14.1578 0.0356702C16.0673 -0.237951 17.7836 1.0926 17.982 2.99722C18.1698 4.77844 16.8503 6.42554 15.0696 6.62405C13.8146 6.7689 12.8062 6.31287 12.0178 5.34178C11.991 5.32032 11.9695 5.29349 11.9427 5.25594ZM5.35629 8.99543C5.35629 7.87412 4.43376 6.94595 3.31815 6.95132C2.2079 6.95668 1.29074 7.86876 1.28001 8.97934C1.26928 10.0899 2.19181 11.0234 3.31279 11.0342C4.4284 11.0449 5.35629 10.1221 5.35629 8.99543ZM12.6239 14.661C12.6185 15.7823 13.541 16.7105 14.6566 16.7051C15.7669 16.7051 16.7002 15.7716 16.7002 14.661C16.6948 13.5504 15.7884 12.6437 14.6674 12.633C13.5464 12.6169 12.6292 13.529 12.6239 14.661ZM14.6566 5.35788C15.7776 5.35788 16.7002 4.43507 16.7002 3.31913C16.6948 2.20318 15.7615 1.27501 14.6513 1.28038C13.5357 1.28574 12.6346 2.19782 12.6239 3.31376C12.6185 4.44044 13.5303 5.35788 14.6566 5.35788Z"
                    fill="var(--font-color1)" />
                </svg>
                <svg class="emty_f_hover" xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 18 18"
                  fill="none">
                  <path
                    d="M11.9427 5.25594C10.1298 6.16264 8.32232 7.06399 6.49872 7.97606C6.70254 8.6467 6.7079 9.31734 6.49872 9.99871C8.31159 10.9054 10.1191 11.8121 11.9052 12.7027C12.1841 12.4452 12.4308 12.1662 12.7204 11.957C14.7264 10.4923 17.5905 11.7048 17.9499 14.1674C18.2609 16.3081 16.5124 18.1644 14.3509 17.982C12.817 17.8533 11.5297 16.6085 11.3634 15.0741C11.3313 14.7576 11.3366 14.4303 11.3795 14.1191C11.4063 13.9367 11.3581 13.8777 11.2133 13.808C9.65784 13.0354 8.10778 12.2574 6.55236 11.4795C6.37536 11.3883 6.193 11.3078 6.016 11.2059C5.90873 11.1468 5.84973 11.1629 5.76392 11.2542C5.07202 11.9838 4.22459 12.3379 3.21624 12.3272C1.66082 12.3057 0.244848 11.0556 0.0356701 9.51048C-0.216416 7.69171 0.888471 6.0929 2.69062 5.72807C3.87059 5.492 4.89503 5.8461 5.74246 6.70452C5.8551 6.81719 5.92482 6.82792 6.05891 6.75817C7.7806 5.88902 9.50766 5.0306 11.2347 4.17218C11.3688 4.1078 11.4117 4.04879 11.3795 3.88783C11.047 2.06905 12.3342 0.293196 14.1578 0.0356702C16.0673 -0.237951 17.7836 1.0926 17.982 2.99722C18.1698 4.77844 16.8503 6.42554 15.0696 6.62405C13.8146 6.7689 12.8062 6.31287 12.0178 5.34178C11.991 5.32032 11.9695 5.29349 11.9427 5.25594ZM5.35629 8.99543C5.35629 7.87412 4.43376 6.94595 3.31815 6.95132C2.2079 6.95668 1.29074 7.86876 1.28001 8.97934C1.26928 10.0899 2.19181 11.0234 3.31279 11.0342C4.4284 11.0449 5.35629 10.1221 5.35629 8.99543ZM12.6239 14.661C12.6185 15.7823 13.541 16.7105 14.6566 16.7051C15.7669 16.7051 16.7002 15.7716 16.7002 14.661C16.6948 13.5504 15.7884 12.6437 14.6674 12.633C13.5464 12.6169 12.6292 13.529 12.6239 14.661ZM14.6566 5.35788C15.7776 5.35788 16.7002 4.43507 16.7002 3.31913C16.6948 2.20318 15.7615 1.27501 14.6513 1.28038C13.5357 1.28574 12.6346 2.19782 12.6239 3.31376C12.6185 4.44044 13.5303 5.35788 14.6566 5.35788Z"
                    fill="var(--text-color)" />
                </svg>
                <div class="on_hov">
                  поделиться
                </div>
              </div>
            </button>
            <button class="icons_w queue_hov" (click)="addToQueue()">
              <div class="flex items-center icon-wrap star_w">
                <svg class="emty_f" width="22" height="22" viewBox="0 0 22 22" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M7.04681 11.2727C9.04429 11.2727 12.644 11.2727 14.6415 11.2727C15.1787 11.2727 15.5815 11.5905 15.6738 12.0672C15.7578 12.4962 15.4892 12.9569 15.036 13.0999C14.8765 13.1555 14.6919 13.1714 14.524 13.1714C13.6008 13.1794 12.6776 13.1714 11.746 13.1714C8.76659 13.1714 4.19327 13.1714 1.21384 13.1714C1.0292 13.1714 0.844555 13.1635 0.6767 13.1079C0.231884 12.9728 -0.0534705 12.52 0.0136716 12.083C0.0808137 11.6382 0.466881 11.2965 0.945269 11.2648C1.0292 11.2568 1.11312 11.2648 1.18866 11.2648C3.14417 11.2727 5.0913 11.2727 7.04681 11.2727Z"
                    fill="var(--font-color1)" />
                  <path
                    d="M7.02096 1.91462C5.07384 1.91462 3.11832 1.91462 1.1712 1.91462C1.02013 1.91462 0.860671 1.90667 0.709601 1.85901C0.273177 1.73984 -0.0121766 1.34262 0.00460892 0.90567C0.0297872 0.476668 0.357105 0.111223 0.801922 0.0317779C0.91942 0.00794447 1.04531 0 1.16281 0C5.08223 0 16.8858 0 20.8052 0C21.3004 0 21.6865 0.150945 21.8879 0.60378C22.1733 1.23934 21.6781 1.90667 20.9227 1.91462C19.6386 1.92256 18.3629 1.91462 17.0789 1.91462C16.3487 1.91462 7.74274 1.91462 7.02096 1.91462Z"
                    fill="var(--font-color1)" />
                  <path
                    d="M7.01705 7.5445C5.06993 7.5445 3.11442 7.5445 1.1673 7.5445C1.01623 7.5445 0.856765 7.53656 0.705695 7.48889C0.269271 7.36972 -0.0160829 6.9725 0.000702669 6.53555C0.025881 6.10655 0.353199 5.74111 0.798015 5.66166C0.915514 5.63783 1.04141 5.62988 1.1589 5.62988C5.07833 5.62988 16.8819 5.62988 20.8013 5.62988C21.2965 5.62988 21.6826 5.78083 21.884 6.23366C22.1694 6.86922 21.6742 7.53656 20.9188 7.5445C19.6347 7.55244 18.359 7.5445 17.0749 7.5445C16.3448 7.5445 7.73883 7.5445 7.01705 7.5445Z"
                    fill="var(--font-color1)" />
                  <path
                    d="M4.03261 19.0353C3.02548 19.0353 2.01835 19.0353 1.01122 19.0353C0.549615 19.0353 0.180334 18.7652 0.0460494 18.3521C-0.0798421 17.9628 0.0544422 17.5418 0.423724 17.3273C0.608365 17.216 0.851755 17.1445 1.06157 17.1445C3.04227 17.1286 7.67854 17.1286 9.65923 17.1366C10.2887 17.1366 10.7083 17.5576 10.6999 18.1137C10.6915 18.654 10.2467 19.0353 9.63406 19.0353C8.6605 19.0432 5.01457 19.0353 4.03261 19.0353Z"
                    fill="var(--font-color1)" />
                  <path
                    d="M17.9968 19.2702C16.9897 19.2702 16.659 19.2702 15.6518 19.2702C15.1902 19.2702 14.821 19 14.6867 18.5869C14.5608 18.1977 14.6951 17.7766 15.0643 17.5621C15.249 17.4509 15.4924 17.3794 15.7022 17.3794C17.6829 17.3635 18.9787 17.3635 20.9594 17.3714C21.5889 17.3714 22.0085 17.7925 22.0001 18.3486C21.9917 18.8888 21.5469 19.2702 20.9343 19.2702C19.9607 19.2781 18.9787 19.2702 17.9968 19.2702Z"
                    fill="var(--font-color1)" />
                  <path
                    d="M17.3461 17.9963C17.3461 16.9892 17.3461 16.6585 17.3461 15.6514C17.3461 15.1898 17.6162 14.8205 18.0293 14.6862C18.4186 14.5603 18.8396 14.6946 19.0541 15.0639C19.1653 15.2485 19.2368 15.4919 19.2368 15.7017C19.2527 17.6824 19.2527 18.9782 19.2448 20.9589C19.2448 21.5884 18.8237 22.008 18.2676 21.9996C17.7274 21.9913 17.3461 21.5464 17.3461 20.9338C17.3381 19.9602 17.3461 18.9782 17.3461 17.9963Z"
                    fill="var(--font-color1)" />
                </svg>
                <svg class="emty_f_hover" width="22" height="22" viewBox="0 0 22 22" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M7.04681 11.2727C9.04429 11.2727 12.644 11.2727 14.6415 11.2727C15.1787 11.2727 15.5815 11.5905 15.6738 12.0672C15.7578 12.4962 15.4892 12.9569 15.036 13.0999C14.8765 13.1555 14.6919 13.1714 14.524 13.1714C13.6008 13.1794 12.6776 13.1714 11.746 13.1714C8.76659 13.1714 4.19327 13.1714 1.21384 13.1714C1.0292 13.1714 0.844555 13.1635 0.6767 13.1079C0.231884 12.9728 -0.0534705 12.52 0.0136716 12.083C0.0808137 11.6382 0.466881 11.2965 0.945269 11.2648C1.0292 11.2568 1.11312 11.2648 1.18866 11.2648C3.14417 11.2727 5.0913 11.2727 7.04681 11.2727Z"
                    fill="var(--text-color)" />
                  <path
                    d="M7.02096 1.91462C5.07384 1.91462 3.11832 1.91462 1.1712 1.91462C1.02013 1.91462 0.860671 1.90667 0.709601 1.85901C0.273177 1.73984 -0.0121766 1.34262 0.00460892 0.90567C0.0297872 0.476668 0.357105 0.111223 0.801922 0.0317779C0.91942 0.00794447 1.04531 0 1.16281 0C5.08223 0 16.8858 0 20.8052 0C21.3004 0 21.6865 0.150945 21.8879 0.60378C22.1733 1.23934 21.6781 1.90667 20.9227 1.91462C19.6386 1.92256 18.3629 1.91462 17.0789 1.91462C16.3487 1.91462 7.74274 1.91462 7.02096 1.91462Z"
                    fill="var(--text-color)" />
                  <path
                    d="M7.01705 7.5445C5.06993 7.5445 3.11442 7.5445 1.1673 7.5445C1.01623 7.5445 0.856765 7.53656 0.705695 7.48889C0.269271 7.36972 -0.0160829 6.9725 0.000702669 6.53555C0.025881 6.10655 0.353199 5.74111 0.798015 5.66166C0.915514 5.63783 1.04141 5.62988 1.1589 5.62988C5.07833 5.62988 16.8819 5.62988 20.8013 5.62988C21.2965 5.62988 21.6826 5.78083 21.884 6.23366C22.1694 6.86922 21.6742 7.53656 20.9188 7.5445C19.6347 7.55244 18.359 7.5445 17.0749 7.5445C16.3448 7.5445 7.73883 7.5445 7.01705 7.5445Z"
                    fill="var(--text-color)" />
                  <path
                    d="M4.03261 19.0353C3.02548 19.0353 2.01835 19.0353 1.01122 19.0353C0.549615 19.0353 0.180334 18.7652 0.0460494 18.3521C-0.0798421 17.9628 0.0544422 17.5418 0.423724 17.3273C0.608365 17.216 0.851755 17.1445 1.06157 17.1445C3.04227 17.1286 7.67854 17.1286 9.65923 17.1366C10.2887 17.1366 10.7083 17.5576 10.6999 18.1137C10.6915 18.654 10.2467 19.0353 9.63406 19.0353C8.6605 19.0432 5.01457 19.0353 4.03261 19.0353Z"
                    fill="var(--text-color)" />
                  <path
                    d="M17.9968 19.2702C16.9897 19.2702 16.659 19.2702 15.6518 19.2702C15.1902 19.2702 14.821 19 14.6867 18.5869C14.5608 18.1977 14.6951 17.7766 15.0643 17.5621C15.249 17.4509 15.4924 17.3794 15.7022 17.3794C17.6829 17.3635 18.9787 17.3635 20.9594 17.3714C21.5889 17.3714 22.0085 17.7925 22.0001 18.3486C21.9917 18.8888 21.5469 19.2702 20.9343 19.2702C19.9607 19.2781 18.9787 19.2702 17.9968 19.2702Z"
                    fill="var(--text-color)" />
                  <path
                    d="M17.3461 17.9963C17.3461 16.9892 17.3461 16.6585 17.3461 15.6514C17.3461 15.1898 17.6162 14.8205 18.0293 14.6862C18.4186 14.5603 18.8396 14.6946 19.0541 15.0639C19.1653 15.2485 19.2368 15.4919 19.2368 15.7017C19.2527 17.6824 19.2527 18.9782 19.2448 20.9589C19.2448 21.5884 18.8237 22.008 18.2676 21.9996C17.7274 21.9913 17.3461 21.5464 17.3461 20.9338C17.3381 19.9602 17.3461 18.9782 17.3461 17.9963Z"
                    fill="var(--text-color)" />
                </svg>
                <div class="on_hov">
                  добавить в очередь
                </div>
              </div>
            </button>
            <button class="icons_w fav_hov btn-favourite"
              [ngClass]="{'in-favourites': selectedTrackId | isInPlaylist : playlists}"
              (click)="showPlaylistDialog(); $event.stopPropagation();">
              <div class="icon-wrap star_w">
                <app-favorites-icon></app-favorites-icon>
                <div class="on_hov">
                  добавить в избранное
                </div>
              </div>
            </button>
            <button class="flex items-center icons_w like_hov btn_like" [ngClass]="{'is-liked': track().liked}"
              (click)="like(); $event.stopPropagation();">
              <div class="flex items-center icon-wrap like_w ">
                <svg class="emty_l" width="24" height="22" viewBox="0 0 24 22" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                    fill="var(--font-color1)" />
                </svg>
                <svg class="emty_l_hover" width="24" height="22" viewBox="0 0 24 22" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                    fill="var(--text-color)" />
                </svg>
                <div class="on_hov">
                  мне нравится
                </div>
              </div>
              <span class="ml-2 pt-[2px]">{{track().likes}}</span>
            </button>
          </div>
        </div>
        <div class="flex items-center justify-between w-full">

          <!-- <div class="audio-views flex items-center mr-3">
            <img class="mr-2" src="assets/images/icons/eye.svg" alt="">
            {{track().views}}
          </div> -->

        </div>
        @if(audioService.hasAccess(track())) {
          <app-audio-player [track]="track" [play]="forceToPlay" class="block w-full"></app-audio-player>
        } @else {
          <span style="color: red">Для прослушивания и скачивания аудио оформите подписку</span>
        }


        <p class="track-description">{{ track().description }}</p>
        @if(track().text && track().textLink) {
        <a (click)="downloadText($event)" style="color: blue; text-decoration: underline" download>Скачать текст
          лекции</a>
        }

        <div class="audio_chips w-full" *ngIf="track().tags">
          @for(tag of track().tags; track tag.id) {
          <div class="audio_chip" (click)="goToListByTag(tag.id)">{{tag.name}}</div>
          }
        </div>

        <a class="read-btn" *ngIf="track().text_link" [routerLink]="getAbsolutePath(track().text_link)">
          <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
          <div class="read-btn-label">Читать</div>
        </a>

        <div class="lotus_divider">
          <img src="../../../assets/images/icons/lotus_divider.svg" alt="divider">
        </div>

        @if(similar.length) {
        <div class="similar-content-section w-full">
          <h3 class="similar-content-title">
            Похожие лекции:
            <div class="mobile-similar-buttons">
              <div class="prev-button">
                <img src="assets/images/icons/arrow-in-circle.svg" alt="prev"
                  (click)="moveLeft()">
              </div>
              <div class="next-button">
                <img src="assets/images/icons/arrow-in-circle.svg" alt="next"
                  (click)="moveRight()">
              </div>
            </div>
          </h3>
          <div id="carousel" class="similar_container">
            @for(item of similar; track $index) {

            <div class="similar-item">
              <!-- <div class="img_similar">
                    <img [src]="item.image ? environment.apiUrl + '/uploads/' + item.image : 'assets/images/default-preview.avif'"
                           [alt]="item.title" />
                  </div> -->
              <button class="play-pause-similar"
                (click)="goToAnotherAudio(item.external_id);">
                <svg width="56" height="56" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M28 56C43.464 56 56 43.464 56 28C56 12.536 43.464 0 28 0C12.536 0 0 12.536 0 28C0 43.464 12.536 56 28 56Z"
                    fill="url(#paint0_linear_39_15966)" />
                  <path
                    d="M22.4929 19.25C22.6364 19.25 22.7799 19.25 22.9377 19.25C23.3681 19.3302 23.7699 19.4773 24.143 19.6778C28.2753 21.8971 32.4219 24.1029 36.5543 26.3222C36.8556 26.4826 37.1425 26.6832 37.3865 26.9104C38.0608 27.5254 38.0895 28.3142 37.4869 28.996C37.2286 29.2901 36.8986 29.504 36.5399 29.6912C32.4076 31.8971 28.2753 34.1163 24.1286 36.3222C23.8273 36.4826 23.4973 36.6029 23.1673 36.6965C22.2633 36.9238 21.5029 36.5495 21.1729 35.734C21.015 35.3596 20.9863 34.9719 20.9863 34.5709C21.0007 30.1858 21.0007 25.8008 21.0007 21.4291C21.0007 21.2553 21.0007 21.0816 21.0294 20.8944C21.0868 20.4131 21.2446 19.9719 21.632 19.6243C21.8759 19.4104 22.1772 19.3168 22.4929 19.25Z"
                    fill="white" />
                  <defs>
                    <linearGradient id="paint0_linear_39_15966" x1="13.0345" y1="2.89655" x2="43.931" y2="53.5862"
                      gradientUnits="userSpaceOnUse">
                      <stop offset="0.157692" stop-color="var(--pl_start1)" />
                      <stop offset="0.85344" stop-color="var(--pl_stop1)" />
                    </linearGradient>
                  </defs>
                </svg>
              </button>
              <div (click)="goToAnotherAudio(item.external_id);"
                class="similar-item_date flex gap-2 justify-center items-center cursor-pointer">
                <svg width="22" height="24" viewBox="0 0 22 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M6.41924 5.02326C5.96157 5.02326 5.58203 4.64372 5.58203 4.18605V0.837209C5.58203 0.379535 5.96157 0 6.41924 0C6.87691 0 7.25645 0.379535 7.25645 0.837209V4.18605C7.25645 4.64372 6.87691 5.02326 6.41924 5.02326Z"
                    fill="var(--text-color)" />
                  <path
                    d="M15.3489 5.02326C14.8913 5.02326 14.5117 4.64372 14.5117 4.18605V0.837209C14.5117 0.379535 14.8913 0 15.3489 0C15.8066 0 16.1861 0.379535 16.1861 0.837209V4.18605C16.1861 4.64372 15.8066 5.02326 15.3489 5.02326Z"
                    fill="var(--text-color)" />
                  <path
                    d="M6.97565 14.7908C6.83054 14.7908 6.68542 14.7573 6.55147 14.7015C6.40635 14.6457 6.29472 14.5676 6.1831 14.4671C5.98217 14.255 5.85938 13.9759 5.85938 13.6745C5.85938 13.5294 5.89286 13.3843 5.94868 13.2503C6.00449 13.1164 6.08263 12.9936 6.1831 12.882C6.29472 12.7815 6.40635 12.7034 6.55147 12.6476C6.95333 12.4801 7.45565 12.5694 7.76821 12.882C7.96914 13.0941 8.09193 13.3843 8.09193 13.6745C8.09193 13.7415 8.08077 13.8196 8.06961 13.8978C8.05844 13.9648 8.03612 14.0317 8.00263 14.0987C7.98031 14.1657 7.94682 14.2327 7.90217 14.2996C7.86868 14.3555 7.81286 14.4113 7.76821 14.4671C7.55612 14.668 7.26589 14.7908 6.97565 14.7908Z"
                    fill="var(--text-color)" />
                  <path
                    d="M10.8819 14.7909C10.7368 14.7909 10.5917 14.7574 10.4577 14.7016C10.3126 14.6458 10.201 14.5676 10.0893 14.4672C9.88842 14.2551 9.76562 13.976 9.76562 13.6746C9.76562 13.5295 9.79911 13.3844 9.85493 13.2504C9.91074 13.1165 9.98888 12.9937 10.0893 12.8821C10.201 12.7816 10.3126 12.7034 10.4577 12.6476C10.8596 12.469 11.3619 12.5695 11.6745 12.8821C11.8754 13.0941 11.9982 13.3844 11.9982 13.6746C11.9982 13.7416 11.987 13.8197 11.9759 13.8979C11.9647 13.9648 11.9424 14.0318 11.9089 14.0988C11.8866 14.1658 11.8531 14.2327 11.8084 14.2997C11.7749 14.3555 11.7191 14.4114 11.6745 14.4672C11.4624 14.6681 11.1721 14.7909 10.8819 14.7909Z"
                    fill="var(--text-color)" />
                  <path
                    d="M14.7921 14.7904C14.6469 14.7904 14.5018 14.7569 14.3679 14.7011C14.2228 14.6453 14.1111 14.5671 13.9995 14.4667C13.9549 14.4109 13.9102 14.3551 13.8655 14.2992C13.8209 14.2323 13.7874 14.1653 13.7651 14.0983C13.7316 14.0313 13.7093 13.9644 13.6981 13.8974C13.6869 13.8192 13.6758 13.7411 13.6758 13.6741C13.6758 13.3839 13.7986 13.0937 13.9995 12.8816C14.1111 12.7811 14.2228 12.703 14.3679 12.6471C14.7809 12.4685 15.2721 12.569 15.5846 12.8816C15.7855 13.0937 15.9083 13.3839 15.9083 13.6741C15.9083 13.7411 15.8972 13.8192 15.886 13.8974C15.8749 13.9644 15.8525 14.0313 15.819 14.0983C15.7967 14.1653 15.7632 14.2323 15.7186 14.2992C15.6851 14.3551 15.6293 14.4109 15.5846 14.4667C15.3725 14.6676 15.0823 14.7904 14.7921 14.7904Z"
                    fill="var(--text-color)" />
                  <path
                    d="M6.97565 18.6975C6.83054 18.6975 6.68542 18.6641 6.55147 18.6083C6.41751 18.5525 6.29472 18.4742 6.1831 18.3738C5.98217 18.1617 5.85938 17.8715 5.85938 17.5812C5.85938 17.4361 5.89286 17.291 5.94868 17.157C6.00449 17.0119 6.08263 16.8892 6.1831 16.7888C6.59612 16.3757 7.35519 16.3757 7.76821 16.7888C7.96914 17.0009 8.09193 17.291 8.09193 17.5812C8.09193 17.8715 7.96914 18.1617 7.76821 18.3738C7.55612 18.5747 7.26589 18.6975 6.97565 18.6975Z"
                    fill="var(--text-color)" />
                  <path
                    d="M10.8819 18.6975C10.5917 18.6975 10.3014 18.5747 10.0893 18.3738C9.88842 18.1617 9.76562 17.8715 9.76562 17.5812C9.76562 17.4361 9.79911 17.291 9.85493 17.157C9.91074 17.0119 9.98888 16.8892 10.0893 16.7888C10.5024 16.3757 11.2614 16.3757 11.6745 16.7888C11.7749 16.8892 11.8531 17.0119 11.9089 17.157C11.9647 17.291 11.9982 17.4361 11.9982 17.5812C11.9982 17.8715 11.8754 18.1617 11.6745 18.3738C11.4624 18.5747 11.1721 18.6975 10.8819 18.6975Z"
                    fill="var(--text-color)" />
                  <path
                    d="M14.7921 18.6974C14.5018 18.6974 14.2116 18.5746 13.9995 18.3737C13.899 18.2732 13.8209 18.1504 13.7651 18.0053C13.7093 17.8714 13.6758 17.7262 13.6758 17.5811C13.6758 17.436 13.7093 17.2909 13.7651 17.1569C13.8209 17.0118 13.899 16.889 13.9995 16.7886C14.2562 16.5318 14.6469 16.409 15.0042 16.4872C15.0823 16.4983 15.1493 16.5207 15.2162 16.5542C15.2832 16.5765 15.3502 16.61 15.4172 16.6546C15.473 16.6881 15.5288 16.7439 15.5846 16.7886C15.7855 17.0007 15.9083 17.2909 15.9083 17.5811C15.9083 17.8714 15.7855 18.1616 15.5846 18.3737C15.3725 18.5746 15.0823 18.6974 14.7921 18.6974Z"
                    fill="var(--text-color)" />
                  <path
                    d="M20.3725 9.58848H1.3958C0.938129 9.58848 0.558594 9.20895 0.558594 8.75127C0.558594 8.2936 0.938129 7.91406 1.3958 7.91406H20.3725C20.8302 7.91406 21.2098 8.2936 21.2098 8.75127C21.2098 9.20895 20.8302 9.58848 20.3725 9.58848Z"
                    fill="var(--text-color)" />
                  <path
                    d="M15.3488 23.9999H6.4186C2.34419 23.9999 0 21.6557 0 17.5813V8.09292C0 4.0185 2.34419 1.67432 6.4186 1.67432H15.3488C19.4233 1.67432 21.7674 4.0185 21.7674 8.09292V17.5813C21.7674 21.6557 19.4233 23.9999 15.3488 23.9999ZM6.4186 3.34874C3.22605 3.34874 1.67442 4.90036 1.67442 8.09292V17.5813C1.67442 20.7739 3.22605 22.3255 6.4186 22.3255H15.3488C18.5414 22.3255 20.093 20.7739 20.093 17.5813V8.09292C20.093 4.90036 18.5414 3.34874 15.3488 3.34874H6.4186Z"
                    fill="var(--text-color)" />
                </svg>
                <span>
                  {{item.date | date:'dd.MM.yy'}}
                </span>
              </div>
              <div (click)="goToAnotherAudio(item.external_id);forceToPlay = !forceToPlay;"
                class="similar-item_name cursor-pointer">
                {{item.title}}
              </div>
            </div>
            }
          </div>
          <!-- <div class="similar-items-grid">
              @for(item of similar; track $index) {
                <a class="similar-item-card"
                    [routerLink]="getAbsolutePath(track().text_link)"
                   >
                  <div class="similar-item-image">
                    <img [src]="item.image ? environment.apiUrl + '/uploads/' + item.image : 'assets/images/default-preview.avif'"
                         [alt]="item.title" />
                  </div>
                  <div class="similar-item-content">
                    <h4 class="similar-item-title">{{item.title}}</h4>
                  </div>
                </a>
              }
            </div> -->
        </div>
        }

      </div>
      <!-- <div class="flex flex-col items-center w-full" *ngIf="activeTab === 1"> // TODO: uncomment if will be logic for download Lecture text
        <p>{{ track().description }}</p>
        @if(track().text && track().textLink) {
        <a (click)="downloadText($event)" style="color: blue; text-decoration: underline" download>Скачать текст лекции</a>
        }
      </div> -->
      <div class="flex flex-col items-center w-full" *ngSwitchCase="LectureTab.VIDEO">
        <div class="flex items-center justify-between video-head w-full">
          <div class="flex gap-3 info-head">
            <div class="flex gap-[6px] items-center">
              <span class="desktop-duration">Длительность:</span>
              <span class="mobile-duration">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M7.5 0C7.83333 0 8.16667 0 8.5 0C8.54167 0.00835073 8.57917 0.0208768 8.62083 0.0250522C9.12083 0.112735 9.64167 0.141962 10.1208 0.292276C13.2792 1.26514 15.2042 3.37787 15.875 6.62213C15.9333 6.91023 15.9583 7.20668 16 7.49896C16 7.83298 16 8.16701 16 8.50104C15.9917 8.5428 15.9792 8.58037 15.975 8.62213C15.8917 9.72025 15.6125 10.7683 15.075 11.7328C13.5625 14.4301 11.2583 15.8747 8.16667 15.9958C6.50417 16.0626 4.9625 15.5992 3.57917 14.6681C1.69583 13.3904 0.541667 11.6326 0.125 9.39457C0.0708333 9.09812 0.0416667 8.79749 0 8.50104C0 8.16701 0 7.83298 0 7.49896C0.00833333 7.45303 0.0208333 7.4071 0.025 7.36117C0.0791667 6.99791 0.1125 6.6263 0.1875 6.26722C0.833333 3.16493 3.47917 0.634655 6.60417 0.125261C6.90417 0.0751566 7.2 0.0417536 7.5 0ZM1.28333 8.6263C1.58333 12.0084 4.36667 14.4885 7.375 14.7098C7.375 14.5344 7.375 14.3591 7.375 14.1795C7.37917 13.7954 7.6375 13.524 8 13.524C8.3625 13.524 8.62083 13.7954 8.625 14.1795C8.625 14.3549 8.625 14.5303 8.625 14.7098C11.8583 14.476 14.5 11.7161 14.7042 8.6263C14.625 8.6263 14.55 8.6263 14.4708 8.6263C13.8083 8.6263 13.5208 8.43424 13.5208 7.99582C13.525 7.56576 13.8083 7.37369 14.4625 7.37369C14.5458 7.37369 14.625 7.37369 14.7083 7.37369C14.4333 3.9499 11.525 1.46138 8.625 1.29854C8.625 1.46973 8.625 1.64092 8.625 1.81211C8.62083 2.20459 8.3625 2.48017 7.99167 2.47599C7.63333 2.47182 7.375 2.20042 7.375 1.81211C7.375 1.63674 7.375 1.46138 7.375 1.28601C3.83333 1.59499 1.4375 4.60125 1.3 7.37787C1.4625 7.37787 1.62083 7.37787 1.78333 7.37787C2.19583 7.37787 2.47083 7.62839 2.475 8C2.475 8.37578 2.2 8.6263 1.775 8.63048C1.61667 8.6263 1.45417 8.6263 1.28333 8.6263Z"
                    fill="var(--font-color1)" />
                  <path
                    d="M7.37515 6.15856C7.37515 5.56148 7.37515 4.96023 7.37515 4.36315C7.37515 3.97067 7.63765 3.69092 8.00015 3.69092C8.36265 3.69092 8.62515 3.97067 8.62515 4.36733C8.62515 5.43622 8.62932 6.50094 8.62098 7.56983C8.62098 7.69509 8.65432 7.78278 8.74182 7.87046C9.45015 8.57192 10.1501 9.27756 10.8543 9.97902C11.0043 10.1293 11.1001 10.3047 11.0793 10.5218C11.0543 10.7765 10.921 10.9602 10.6793 11.0521C10.4251 11.1481 10.196 11.0897 10.0043 10.8976C9.55848 10.4592 9.12098 10.0166 8.67932 9.57401C8.32932 9.22328 7.98348 8.87255 7.62932 8.52599C7.45432 8.3548 7.37098 8.16273 7.37515 7.91639C7.37932 7.32349 7.37515 6.73893 7.37515 6.15856Z"
                    fill="var(--font-color1)" />
                </svg>
              </span>
              <span>
                {{Math.ceil(track().duration / 60)}} мин.
              </span>
            </div>
            <div class="flex gap-[6px] items-center">
              <span class="auditions-desktop">Просмотренно:</span>
              <span class="auditions-mobile">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="16" viewBox="0 0 20 16" fill="none">
                  <path
                    d="M18.1442 14.646C18.0874 14.8073 18.0448 14.9733 17.9644 15.1251C17.6661 15.6801 17.1974 15.9837 16.5678 15.9932C15.8672 16.0026 15.1618 15.9979 14.4612 15.9932C14.1061 15.9932 13.8931 15.775 13.8931 15.4192C13.8931 13.3179 13.8931 11.2166 13.8931 9.12009C13.8931 8.75011 14.1061 8.5414 14.4754 8.5414C14.9819 8.5414 15.4885 8.5414 16.0139 8.5414C16.0897 6.03219 15.2281 3.94513 13.1972 2.46996C10.8634 0.776599 8.32119 0.582123 5.78377 1.93871C3.20847 3.31902 2.07704 5.61004 2.11965 8.5414C2.19539 8.5414 2.25694 8.5414 2.31848 8.5414C2.77768 8.5414 3.24161 8.5414 3.70081 8.5414C4.03692 8.5414 4.25468 8.7596 4.25468 9.09637C4.25468 11.2071 4.25468 13.3227 4.25468 15.4334C4.25468 15.7797 4.03692 15.9979 3.69134 15.9979C2.99071 15.9979 2.28534 16.0026 1.58471 15.9979C0.708917 15.9931 0.0035505 15.2864 0.0035505 14.4184C-0.0011835 12.9906 -0.0011835 11.5676 0.0035505 10.1399C0.0082845 9.47109 0.330197 8.99202 0.917213 8.69793C1.0403 8.63627 1.07344 8.57461 1.0687 8.44654C1.03083 7.39827 1.14445 6.36897 1.5137 5.38236C2.47943 2.79251 4.28309 1.06594 6.93886 0.321241C9.49523 -0.394999 11.8812 0.0935619 13.9973 1.71578C15.55 2.90635 16.511 4.49061 16.9276 6.40691C17.0744 7.08046 17.1028 7.7635 17.0838 8.45128C17.0791 8.56986 17.1075 8.63153 17.2306 8.68845C17.6898 8.9019 17.9786 9.26713 18.1111 9.76044C18.1206 9.79364 18.1348 9.8221 18.149 9.85056C18.1442 11.4538 18.1442 13.0476 18.1442 14.646ZM3.1848 9.60391C2.66879 9.60391 2.17172 9.60391 1.66992 9.60391C1.28647 9.60391 1.06397 9.8221 1.06397 10.2063C1.06397 11.5771 1.06397 12.9527 1.06397 14.3235C1.06397 14.7077 1.28173 14.9259 1.66992 14.9306C2.12912 14.9306 2.59305 14.9306 3.05225 14.9306C3.09485 14.9306 3.14219 14.9212 3.18953 14.9164C3.1848 13.1472 3.1848 11.3921 3.1848 9.60391ZM14.9677 9.60391C14.9677 11.3921 14.9677 13.1566 14.9677 14.9306C15.034 14.9306 15.0861 14.9306 15.1382 14.9306C15.6116 14.9306 16.085 14.9354 16.5536 14.9259C16.8566 14.9212 17.0744 14.6935 17.0791 14.3852C17.0838 12.9717 17.0838 11.5582 17.0791 10.1446C17.0791 9.84107 16.8566 9.61339 16.5536 9.60391C16.0281 9.59916 15.5027 9.60391 14.9677 9.60391Z"
                    fill="var(--font-color1)" />
                  <path
                    d="M9.60568 9.29554C9.60568 9.40938 9.60568 9.47105 9.60568 9.53745C9.60568 10.7897 9.60568 12.0467 9.60568 13.2989C9.60568 14.148 8.99499 14.85 8.19021 14.9306C7.33336 15.016 6.57118 14.4468 6.43863 13.5788C6.25874 12.4498 7.28128 11.5012 8.42218 11.7858C8.45532 11.7953 8.48845 11.8 8.54526 11.8142C8.54526 11.7384 8.54526 11.6767 8.54526 11.615C8.54526 10.4339 8.55 9.24811 8.54526 8.06703C8.54526 7.80614 8.63994 7.61641 8.87664 7.5168C9.10861 7.42194 9.31217 7.4836 9.48733 7.6591C10.1927 8.36585 10.9028 9.06787 11.6034 9.78411C12.3703 10.5715 12.3656 11.8569 11.6082 12.6206C11.3005 12.9289 10.8271 12.8341 10.6945 12.4309C10.6282 12.2364 10.6803 12.0609 10.8081 11.9044C11.2294 11.4063 11.2058 10.9083 10.7418 10.4434C10.3773 10.0734 10.0128 9.70347 9.60568 9.29554ZM8.54053 13.3274C8.53579 13.0428 8.28016 12.7961 8.00085 12.8009C7.72628 12.8103 7.48485 13.0522 7.48011 13.3274C7.47538 13.6072 7.73101 13.8634 8.01032 13.8634C8.29436 13.8634 8.54526 13.6072 8.54053 13.3274Z"
                    fill="var(--font-color1)" />
                </svg>
              </span>
              <span>
                {{track().views}}
              </span>
            </div>
          </div>
          <div class="head_action_panel">
            <button class="flex items-center icons_w like_hov btn_like" [ngClass]="{'is-liked': track().liked}"
              (click)="like(); $event.stopPropagation();">
              <div class="flex items-center like_w icon-wrap">
                <svg class="emty_l" width="24" height="22" viewBox="0 0 24 22" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                    fill="var(--font-color1)" />
                </svg>
                <svg class="emty_l_hover" width="24" height="22" viewBox="0 0 24 22" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                    fill="var(--text-color)" />
                </svg>
                <div class="on_hov">
                  мне нравится
                </div>
              </div>
              <span class="ml-2 pt-[2px]">{{track().likes}}</span>
            </button>
          </div>
        </div>
        <div class="video-container">
          @if(sanitizedVideoUrl) {
          <iframe [src]="sanitizedVideoUrl" width="100%" height="509px" frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowfullscreen>
          </iframe>
          }
        </div>
      </div>
      <!-- <div class="flex flex-col items-center w-full" *ngIf="activeTab === 3">
        <div>
          {{track().text}}
        </div>
      </div> -->


    </div>
  </div>
</div>

<playlist-dialog [showPlaylist]="showPlaylist" [selectedTrackId]="selectedTrackId"
  (playlistClosed)="playlistClosed($event)" (playlistSelected)="playlistSelected($event)">
</playlist-dialog>
