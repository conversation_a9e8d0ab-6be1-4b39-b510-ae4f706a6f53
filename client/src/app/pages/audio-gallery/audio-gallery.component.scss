@use "../../styles/core.scss" as core;
@use "../categories/category/category.component.scss";

input,
select {
  border: 1px solid core.$light2;
  border-radius: 5px;
  margin-right: 10px;
  margin-bottom: 10px;
  width: 230px;
}

.save-btn:last-of-type {
  margin: 60px 0 35px 0 !important;
}

.save-btn {
  width: 218px;
  height: 54px;
  padding: 0;
  position: relative;
  margin: 60px 25px 35px 0 !important;

  .btn-backdrop-img {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    object-fit: contain;
  }

  .save-btn-label {
    margin: 0 auto;
    font-family: Prata;
    font-weight: 400;
    font-size: 20px;
    line-height: 20px;
    letter-spacing: 0%;
    text-align: center;
    vertical-align: middle;
    padding: 14px 25px;
    color: var(--font-color1);
  }
}

.scroll_w {
  max-height: 250px;
  overflow: auto;

  &::-webkit-scrollbar {
    width: 10px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--book_about);
    border-radius: 5px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: var(--text-color);
  }

  &::-webkit-scrollbar:horizontal {
    height: 6px;
  }

  &::-webkit-scrollbar-thumb:horizontal {
    border-radius: 5px;
  }
}

.art_img {
  width: 52px;
  height: 52px;
  min-width: 52px;
}

.mt-u_ {
  margin-top: 24px;
}

.art_img_ {
  height: 56px;
}

.vis_md {
  display: none;
}

.art_img_::before {
  margin-right: 21px;
}

.cat_wrap {
  margin: -60px auto 20px auto;
}

.actions_w {
  top: unset;
}

.article-item {
  padding: 24px 0;
  max-height: 172px;
  transition: max-height 0.5s ease-out, padding 0.5s ease-out;

  .invis_part {
    position: relative;
    opacity: 0;
    padding: 8px 0 0 121px;
    transition: opacity 0.3s ease-out;
    z-index: -1;
  }

  &.widen {
    max-height: 1008px;

    .invis_part {
      opacity: 1;
      z-index: 1;
    }
  }
}

.article-title {
  margin-bottom: 12px;
  white-space: unset;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.titl_w {
  margin-left: 28px;

  .icons_w.b_p {
    margin-top: 19px;
  }

  .cal_w {
    margin-left: 0;
  }
}

button.color {
  background-color: #c6c6c6;
  border-radius: 5px;
  margin: 0 10px;
  padding: 0 6px;
}

.clamp_3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
}

.p-2 {
  background-color: #edeae6;
  width: fit-content;
  margin: 20px auto;
  border-radius: 10px;
}

.btn-like svg {
  width: 24px;
}

.md_chg {
  display: flex;
}

.btn-like svg path {
  fill: transparent;
  stroke: gray;
}

.btn-like.is-liked path {
  fill: red !important;
  stroke: none !important;
}

.btn-favourite svg {
  width: 20px;
  height: 20px;
}

.btn-favourite.in-favourites svg {
  fill: #ffc94a;
  stroke: none;
}

.show_p_md {
  display: none;
  font-family: Prata;
  font-weight: 400;
  font-size: 15px;
  line-height: 11px;
  color: var(--font-color1);
  white-space: nowrap;
}

.audio-views {
  margin-left: 30px;
}

.audio-views img {
  width: 22px;
  height: 22px;
  margin-top: -2px;
}

.cont_mod {
  width: 100%;
}

.btn_item_ {
  border-radius: 10px;
  display: flex;
  align-items: center;
  white-space: nowrap;
  font-family: Prata;
  font-weight: 400;
  font-size: 17px;
  line-height: 24px;
  color: var(--font-color1);
}

.tr_descr {
  font-family: Prata;
  font-weight: 400;
  font-size: 17px;
  line-height: 24px;
  color: var(--font-color1);
  max-width: 60%;
  margin-top: 16px;
}

.bs_wrapper_ {
  position: absolute;
  bottom: 24px;
  right: 0;
}

.btn_item_wrapper {
  width: 145px;
  height: 40px;
  border: 1px solid var(--book_about);
  background: transparent;
  // background-clip: padding-box,
  //   border-box;
  // background-origin: padding-box,
  //   border-box;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20px;
  cursor: pointer;
}

.playlist-select {
  position: absolute;
  width: 100%;
  padding: 10px;
  background: white;
  box-shadow: 0px 6px 10px -10px;
}

.playlist-select select {
  margin: 0;
  width: 100%;
  max-width: 100%;
}


#dialog-playlist .ng-input input {
  border: 1px solid gray
}

.audio-listened {
  padding: 3px 7px;
  background: #24ca16;
  color: white;
  border-radius: 6px;
  font-size: 12px;
  margin-right: 10px;
}

.filter-tags input {
  margin-bottom: 0px;
}

.multi-select-container {
  display: flex;
  flex-direction: column;
  margin-right: 10px;
}

.dropdown {
  position: relative;
}

.dropdownF,
.dropdownA {
  position: relative;
}

.dropdown-button {
  border: 1px solid core.$light2;
  border-radius: 5px;
  margin-right: 0;
  margin-bottom: 10px;
  width: 230px;
  height: 30px;
  background-color: core.$light1;
  text-align: left;
  padding-left: 5px;
  line-height: 1.9;
}

input.stlzd {
  border: 1px solid core.$light2;
  border-radius: 5px;
  margin-right: 10px;
  margin-bottom: 10px;
  width: 230px;
  height: 30px;
  background-color: core.$light1;
  text-align: left;
  padding-left: 5px;
}

input.stlzd:focus-visible {
  outline: none;
}

input.stlzd::placeholder {
  color: black;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: white;
  border: 1px solid core.$light2;
  border-radius: 4px;
  width: 230px;
  z-index: 1000;
  margin-top: -10px;
}

// .dropdown-item {
//   display: flex;
//   align-items: center;
//   padding: 5px;
//   cursor: pointer;

//   input {
//     width: unset;
//   }
// }

// .dropdown-item:hover {
//   background-color: core.$light3;
// }

input[type='checkbox'] {
  margin-right: 10px;
}

@media (max-width: 900px) {
  .tr_descr {
    max-width: 50%;
  }

  .article-title {
    max-width: 69%;
    min-width: 69%;
  }
}

@media (max-width: 768px) {

  .dropdown-item {
    white-space: unset;
  }

  .article-title {
    max-width: 94%;
    min-width: 94%;
  }

  .bs_wrapper_ {
    display: none;
  }

  .mt-u_ {
    margin-top: 18px;
  }

  .bs_wrapper_.vis_md {
    position: relative;
    display: flex;
    bottom: unset;
    right: unset;
    margin-top: 14px;
    left: 92px;
    max-width: 270px;
  }

  .tr_descr {
    max-width: 100%;
    margin-top: 10px;
  }

  .btn_item_ {
    font-size: 15px;
    line-height: 15px;
  }

  .article-item.widen {
    .bs_wrapper_.vis_md {
      position: absolute;
      bottom: 27px;
    }
  }

  .btn_item_wrapper {
    width: unset;
    height: 31px;
    padding: 0 13px;
  }

  .middle_stripe {
    padding: 0 94px;
  }

  .audio-views {
    margin-left: 15px;
  }

  .titl_w .icons_w.b_p {
    margin-top: 16px;
  }

  .md_chg {
    display: none;
  }

  .actions_w.show_menu {

    .md_chg {
      display: flex;
    }

    .icon-wrap:not(.no_h) {
      margin-top: 0;
      margin-right: 10px;
    }

    .show_p_md {
      display: flex;
    }

    .icons_w:hover:not(.show_md) {
      background-color: var(--selection);
    }

    .icons_w.is-liked .like_w {
      height: 14px;
      width: 14px;
    }

    .icons_w {
      display: flex;
      align-items: center;
      margin-left: 0;
      padding-left: 14px;
      height: 24px;

      &.show_md {
        height: unset;
      }

      .icon-wrap {
        &.share_w {
          svg {
            width: 14px;
            height: 14px;
          }
        }

        &.star_w:not(.no_h) {
          svg {
            width: 14px;
            height: 14px;
          }
        }
      }

      .icon-wrap.like_w {
        svg {
          width: 14px;
          height: 14px;
        }
      }

      .default_ {
        display: none;
      }
    }
  }

    .md_chg {
      flex-direction: column;
      background-color: #fff;
      position: absolute;
      width: 214px;
      min-height: 122px;
      right: 11px;
      top: 16px;
      z-index: 12;
      border: 1px solid rgb(209 144 54);
      border-top-left-radius: 15px;
      border-bottom-right-radius: 15px;
      border-bottom-left-radius: 15px;
      padding: 13px 0;
    }

  // .md_chg::before {
  //   content: '';
  //   background-image: url(../../../assets/images/icons/tri__.svg);
  //   background-repeat: no-repeat;
  //   background-position: center;
  //   background-size: contain;
  //   display: flex;
  //   width: 22px;
  //   height: 18px;
  //   position: absolute;
  //   right: -2px;
  //   top: -16px;
  // }

  .audio-views img {
    width: 20px;
    height: 20px;
  }

  .titl_w {
    margin-left: 21px;
  }

  .article-item {
    min-height: 190px;
  }

  .article-item.widen .invis_part {
    padding: 8px 0 0 114px;
    margin-bottom: 52px;
  }
}

@media (max-width: 680px) {
  .middle_stripe {
    padding: 0 25px;
  }

  .article-item {
    min-height: 173px;
  }
}

@media (max-width: 570px) {
  .filter-buttons-container {
    .save-btn {
      width: 188px;
      height: 44px;
      margin: 40px 25px 30px 0 !important;
    }

    .save-btn:last-of-type {
      margin: 40px 0 30px 0 !important;
    }
  }

  .save-btn .save-btn-label {
    font-size: 16px;
    line-height: 16px;
  }
}

@media (max-width: 540px) {
  .format-options {
    flex-direction: column;
    align-items: baseline;
  }

  .checkbox-container {
    margin-bottom: 12px;
  }

  .filter-buttons-container {
    .save-btn {
      width: 150px;
      height: 40px;
      margin: 20px 20px 20px 0 !important;
    }

    .save-btn:last-of-type {
      margin: 20px 0 20px 0 !important;
    }
  }

  .save-btn .save-btn-label {
    font-size: 14px;
    line-height: 14px;
  }
}

@media (max-width: 500px) {
  .article-item {
    min-height: 166px;
  }

  .art_img_::before {
    width: 13px;
    height: 7px;
    background-size: contain;
    margin-right: 15px;
  }

  .titl_w {
    margin-left: 12px;
  }

  .audio-views img {
    width: 15px;
    height: 15px;
  }

  .audio-views img.mr-2 {
    margin-right: 3px;
  }

  .titl_w .icons_w.b_p {
    margin-top: 12px;
  }

  .article-category {
    font-size: 11px;
    line-height: 11px;
  }

  .icons_w span.text-color {
    font-size: 11px;
    line-height: 11px;
  }

  .article-item {
    padding: 20px 0;
  }

  .bs_wrapper_.vis_md {
    left: 74px;
  }

  .article-item.widen .invis_part {
    padding: 0 0 0 85px;
    margin-bottom: 48px;
  }

  .tr_descr {
    font-size: 11px;
    line-height: 17px;
    margin-top: 5px;
  }

  .mt-u_ {
    margin-top: 10px;
  }

  .article-item.widen .bs_wrapper_.vis_md {
    bottom: 22px;
    top: unset !important;
  }

  .btn_item_wrapper {
    height: 28px;
    padding: 0px 15px;
    margin-left: 10px;
  }

  .btn_item_ {
    font-size: 13px;
    line-height: 13px;
  }

  .article-title {
    font-size: 15px;
    line-height: 17px;
    margin-bottom: 8px;
  }

  .art_img_ {
    height: 42px;
  }

  .art_img {
    width: 44px;
    height: 44px;
    min-width: 44px;
  }

  input,
  select {
    width: 125px;
  }

  .dropdown-button {
    width: 125px;
  }

  input.stlzd {
    width: 125px;
  }

  .multi-select-container {
    width: 125px;
  }
}

@media (max-width: 420px) {
  .articles-search {
    background: url(assets/images/icons/filters_.svg);
    background-size: contain !important;
  }

  .dropdown-content {
    position: absolute;
    padding: 10px 0;
    left: -100%;
    top: 65px;
    z-index: 21;
  }

  .p_filter {
    right: 32%;
    width: 50px;
    opacity: 0;
  }

  .articles-sort_ {
    display: flex;
    position: absolute;
    height: 80px;
    width: 25%;
  }

  .filter-buttons-container {
    flex-direction: column;
  }

  .filter-buttons-container .save-btn {
    width: 188px;
    margin: 10px auto !important;

    &:last-of-type {
      margin: 10px auto !important;
    }
  }

  dialog.stylized_wide .cont_mod {
    width: 100%;
  }
}

@media (max-width: 370px) {
  .middle_stripe {
    padding: 0 10px;
  }

  .dropdown-content {
    left: -130%;
  }

  .art_img {
    width: 35px;
    height: 35px;
    min-width: 35px;
  }

  .art_img_ {
    height: 38px;
  }

  .art_img_::before {
    margin-right: 5px;
  }

  .bs_wrapper_.vis_md {
    left: 55px;
    max-width: 222px;
  }

  .article-item.widen .invis_part {
    padding: 0 0 0 67px;
  }

  .buttn_catg {
    margin: 55px auto 0 auto;
  }
}

@media (max-width: 350px) {
  .icons_w span.text-color {
    font-size: 10px;
    line-height: 10px;
  }
}

.a_mg_modal {
  margin-top: 20px;
}